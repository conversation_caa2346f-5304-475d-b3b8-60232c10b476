# Docker Setup for Next.js Application

Este documento explica como dockerizar e executar a aplicação Next.js usando Docker.

## 📋 Pré-requisitos

- Docker instalado (versão 20.10 ou superior)
- Docker Compose instalado (versão 2.0 ou superior)

## 🚀 Opções de Execução

### 1. <PERSON><PERSON><PERSON> Docker Compose (Recomendado)

#### Produção
```bash
# Construir e executar em modo produção
docker-compose up --build

# Executar em background
docker-compose up -d --build
```

#### Desenvolvimento
```bash
# Executar em modo desenvolvimento com hot reload
docker-compose --profile dev up --build app-dev
```

### 2. Usando Docker diretamente

#### Construir a imagem
```bash
# Produção
docker build -t nextjs-app .

# Desenvolvimento
docker build -f Dockerfile.dev -t nextjs-app-dev .
```

#### Executar o container
```bash
# Produção
docker run -p 3000:3000 nextjs-app

# Desenvolvimento
docker run -p 3000:3000 -v $(pwd):/app -v /app/node_modules nextjs-app-dev
```

### 3. Usando o script de build (Linux/macOS)

```bash
# Tornar o script executável
chmod +x docker-build.sh

# Construir imagem de produção
./docker-build.sh

# Construir e executar
./docker-build.sh --run

# Construir imagem de desenvolvimento
./docker-build.sh --dev --run

# Ajuda
./docker-build.sh --help
```

## 🔧 Configurações

### Variáveis de Ambiente

- `NODE_ENV`: Define o ambiente (development/production)
- `NEXT_TELEMETRY_DISABLED`: Desabilita telemetria do Next.js
- `BUILD_STANDALONE`: Controla o tipo de build (standalone/export)

### Portas

- **3000**: Porta padrão da aplicação Next.js

## 📁 Estrutura dos Arquivos Docker

```
├── Dockerfile              # Dockerfile de produção (multi-stage)
├── Dockerfile.dev          # Dockerfile de desenvolvimento
├── docker-compose.yml      # Configuração do Docker Compose
├── .dockerignore           # Arquivos ignorados no build
├── docker-build.sh         # Script de build automatizado
└── README-Docker.md        # Este arquivo
```

## 🏗️ Detalhes do Build

### Dockerfile de Produção
- **Multi-stage build** para otimização
- **Node.js 18 Alpine** como base
- **Standalone output** para melhor performance
- **Non-root user** para segurança
- **Health check** incluído

### Dockerfile de Desenvolvimento
- **Hot reload** habilitado
- **Volume mounting** para desenvolvimento
- **Dependências de desenvolvimento** incluídas

## 🚦 Health Check

O container de produção inclui um health check que verifica se a aplicação está respondendo:

```bash
# Verificar status do health check
docker inspect --format='{{.State.Health.Status}}' <container_name>
```

## 🛠️ Comandos Úteis

```bash
# Ver logs do container
docker-compose logs -f

# Parar todos os serviços
docker-compose down

# Remover volumes e imagens
docker-compose down -v --rmi all

# Executar comandos dentro do container
docker-compose exec app sh

# Rebuild sem cache
docker-compose build --no-cache
```

## 🔍 Troubleshooting

### Problema: Porta já em uso
```bash
# Verificar qual processo está usando a porta 3000
lsof -i :3000

# Parar container existente
docker stop $(docker ps -q --filter "publish=3000")
```

### Problema: Permissões no Linux
```bash
# Ajustar permissões do script
chmod +x docker-build.sh

# Executar Docker sem sudo (adicionar usuário ao grupo docker)
sudo usermod -aG docker $USER
```

### Problema: Build lento
- Certifique-se de que o `.dockerignore` está configurado corretamente
- Use `docker system prune` para limpar cache antigo
- Considere usar `docker buildx` para builds paralelos

## 📊 Monitoramento

### Verificar recursos utilizados
```bash
# Uso de recursos do container
docker stats

# Informações detalhadas
docker inspect <container_name>
```

### Logs estruturados
```bash
# Logs com timestamp
docker-compose logs -f -t

# Logs de um serviço específico
docker-compose logs -f app
```

## 🚀 Deploy em Produção

Para deploy em produção, considere:

1. **Registry**: Push da imagem para um registry (Docker Hub, AWS ECR, etc.)
2. **Orquestração**: Use Kubernetes, Docker Swarm ou similar
3. **Reverse Proxy**: Configure Nginx ou Traefik na frente
4. **SSL/TLS**: Configure certificados HTTPS
5. **Monitoring**: Implemente logs e métricas

### Exemplo de push para registry:
```bash
# Tag da imagem
docker tag nextjs-app:latest your-registry/nextjs-app:latest

# Push para registry
docker push your-registry/nextjs-app:latest
```

## 📝 Notas Importantes

- A aplicação está configurada para **export estático** por padrão
- Para usar **standalone mode**, defina `BUILD_STANDALONE=true`
- O **hot reload** funciona apenas no modo desenvolvimento
- **Health checks** estão configurados para produção
- **Security**: Container roda com usuário não-root
