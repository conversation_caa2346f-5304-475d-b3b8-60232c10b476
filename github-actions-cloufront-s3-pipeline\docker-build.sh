#!/bin/bash

# Docker build script for Next.js application

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Default values
IMAGE_NAME="nextjs-app"
TAG="latest"
BUILD_TYPE="production"

# Function to display usage
usage() {
    echo "Usage: $0 [OPTIONS]"
    echo "Options:"
    echo "  -n, --name IMAGE_NAME    Set the Docker image name (default: nextjs-app)"
    echo "  -t, --tag TAG           Set the Docker image tag (default: latest)"
    echo "  -d, --dev               Build development image"
    echo "  -p, --production        Build production image (default)"
    echo "  -r, --run               Run the container after building"
    echo "  -h, --help              Display this help message"
    exit 1
}

# Parse command line arguments
RUN_CONTAINER=false
while [[ $# -gt 0 ]]; do
    case $1 in
        -n|--name)
            IMAGE_NAME="$2"
            shift 2
            ;;
        -t|--tag)
            TAG="$2"
            shift 2
            ;;
        -d|--dev)
            BUILD_TYPE="development"
            shift
            ;;
        -p|--production)
            BUILD_TYPE="production"
            shift
            ;;
        -r|--run)
            RUN_CONTAINER=true
            shift
            ;;
        -h|--help)
            usage
            ;;
        *)
            echo "Unknown option $1"
            usage
            ;;
    esac
done

echo -e "${GREEN}Building Docker image...${NC}"
echo -e "Image name: ${YELLOW}${IMAGE_NAME}:${TAG}${NC}"
echo -e "Build type: ${YELLOW}${BUILD_TYPE}${NC}"

# Build the appropriate Docker image
if [ "$BUILD_TYPE" = "development" ]; then
    echo -e "${YELLOW}Building development image...${NC}"
    docker build -f Dockerfile.dev -t "${IMAGE_NAME}:${TAG}" .
else
    echo -e "${YELLOW}Building production image...${NC}"
    BUILD_STANDALONE=true docker build -t "${IMAGE_NAME}:${TAG}" .
fi

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Docker image built successfully!${NC}"
else
    echo -e "${RED}❌ Docker build failed!${NC}"
    exit 1
fi

# Run container if requested
if [ "$RUN_CONTAINER" = true ]; then
    echo -e "${GREEN}Starting container...${NC}"
    
    # Stop and remove existing container if it exists
    docker stop "${IMAGE_NAME}" 2>/dev/null || true
    docker rm "${IMAGE_NAME}" 2>/dev/null || true
    
    # Run the container
    docker run -d \
        --name "${IMAGE_NAME}" \
        -p 3000:3000 \
        "${IMAGE_NAME}:${TAG}"
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Container started successfully!${NC}"
        echo -e "Application is running at: ${YELLOW}http://localhost:3000${NC}"
    else
        echo -e "${RED}❌ Failed to start container!${NC}"
        exit 1
    fi
fi

echo -e "${GREEN}Done!${NC}"
